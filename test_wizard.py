#!/usr/bin/env python
"""
Test script to verify the venue creation wizard is working correctly.
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project_root.settings')
django.setup()

from django.test import RequestFactory, Client
from django.contrib.auth import get_user_model
from accounts_app.models import ServiceProviderProfile
from venues_app.views.provider import venue_create_wizard_view

User = get_user_model()

def test_wizard_forms():
    """Test that all wizard forms can be instantiated correctly."""
    print("Testing wizard forms...")
    
    from venues_app.forms.venue import (
        VenueBasicInfoForm, VenueLocationContactForm, VenueServicesForm,
        VenueGalleryOnlyForm, VenueDetailsForm
    )
    
    # Test each form
    forms = [
        ('basic', VenueBasicInfoForm),
        ('location', VenueLocationContactForm),
        ('services', VenueServicesForm),
        ('gallery', VenueGalleryOnlyForm),
        ('details', VenueDetailsForm),
    ]
    
    for step, form_class in forms:
        try:
            form = form_class(current_step=step)
            print(f"✓ {step} form: {len(form.fields)} fields, current_step={form.current_step}")
            
            # Test specific fields for location form
            if step == 'location':
                print(f"  - State field required: {form.fields['state'].required}")
                print(f"  - State choices: {len(form.fields['state'].choices)}")
                print(f"  - Phone field required: {form.fields['phone'].required}")
                print(f"  - Email field required: {form.fields['email'].required}")
                
        except Exception as e:
            print(f"✗ {step} form failed: {e}")
            return False
    
    return True

def test_wizard_view():
    """Test that the wizard view works correctly."""
    print("\nTesting wizard view...")
    
    # Get a test user
    user = User.objects.filter(service_provider_profile__isnull=False).first()
    if not user:
        print("✗ No service provider user found")
        return False
    
    print(f"Using test user: {user.email}")
    
    # Create a test client and login
    client = Client()
    client.force_login(user)
    
    # Test each step
    steps = ['basic', 'location', 'services', 'gallery', 'details']
    
    for step in steps:
        try:
            response = client.get(f'/venues/provider/create/wizard/{step}/')
            print(f"✓ {step} step: HTTP {response.status_code}")
            
            if response.status_code != 200:
                print(f"  Response content: {response.content[:200]}")
                
        except Exception as e:
            print(f"✗ {step} step failed: {e}")
            return False
    
    return True

def test_template_rendering():
    """Test that the template renders correctly."""
    print("\nTesting template rendering...")
    
    from django.template.loader import get_template
    from django.template import Context
    
    try:
        template = get_template('venues_app/venue_create_wizard.html')
        print("✓ Template loaded successfully")
        
        # Test context variables
        context = {
            'form': None,  # We'll test without form first
            'current_step': 'location',
            'step_choices': [('basic', 'Basic'), ('location', 'Location')],
            'current_step_title': 'Location',
            'progress_percentage': 40,
            'step_number': 2,
            'total_steps': 5,
        }
        
        # This will fail if there are template syntax errors
        rendered = template.render(context)
        print("✓ Template renders without form")
        
        return True
        
    except Exception as e:
        print(f"✗ Template rendering failed: {e}")
        return False

if __name__ == '__main__':
    print("=== Venue Creation Wizard Test ===\n")
    
    success = True
    success &= test_wizard_forms()
    success &= test_wizard_view()
    success &= test_template_rendering()
    
    print(f"\n=== Test Results ===")
    if success:
        print("✓ All tests passed!")
    else:
        print("✗ Some tests failed!")
    
    sys.exit(0 if success else 1)
